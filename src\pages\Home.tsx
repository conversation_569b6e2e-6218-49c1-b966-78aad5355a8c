import React, { useState, useEffect } from 'react';
import { Search, MapPin } from 'lucide-react';
import SearchBar from '../components/SearchBar';
import FilterPanel from '../components/FilterPanel';
import ResultsSection from '../components/ResultsSection';
import MapView from '../components/MapView';
import { useFilterStore } from '../store/filterStore';
import { Place } from '../types/place';
import { useAuth } from '../context/AuthContext';
import { supabase } from '../lib/supabase';

const Home: React.FC = () => {
  const [places, setPlaces] = useState<Place[]>([]);
  const [filteredPlaces, setFilteredPlaces] = useState<Place[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  
  const { user } = useAuth();
  const { 
    dietaryTags, 
    religionTags, 
    placeType, 
    priceLevel, 
    distance, 
    userLocation,
    searchQuery
  } = useFilterStore();

  // Fetch places from Supabase
  useEffect(() => {
    const fetchPlaces = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const { data, error } = await supabase
          .from('places')
          .select('*');

        if (error) {
          throw new Error(error.message);
        }

        setPlaces(data as Place[]);
        
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
        console.error('Error fetching places:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPlaces();
  }, []);

  // Filter places based on user criteria
  useEffect(() => {
    if (places.length === 0) return;

    setIsLoading(true);

    // Function to calculate distance between two points (haversine formula)
    const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
      const R = 6371; // Radius of the Earth in km
      const dLat = (lat2 - lat1) * Math.PI / 180;
      const dLon = (lon2 - lon1) * Math.PI / 180;
      const a = 
        Math.sin(dLat/2) * Math.sin(dLat/2) +
        Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
        Math.sin(dLon/2) * Math.sin(dLon/2);
      const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
      return R * c; // Distance in km
    };

    // Apply filters
    let filtered = [...places];

    // Filter by place type
    if (placeType !== 'all') {
      filtered = filtered.filter(place => place.type === placeType);
    }

    // Filter by dietary tags
    if (dietaryTags.length > 0) {
      filtered = filtered.filter(place => 
        dietaryTags.every(tag => place.dietary_tags.includes(tag))
      );
    }

    // Filter by religion tags
    if (religionTags.length > 0) {
      filtered = filtered.filter(place => 
        religionTags.every(tag => place.religious_tags.includes(tag))
      );
    }

    // Filter by price level
    if (priceLevel !== null) {
      filtered = filtered.filter(place => place.price_level === priceLevel);
    }

    // Filter by distance if user location is available
    if (userLocation && distance) {
      filtered = filtered.filter(place => {
        const distanceToPlace = calculateDistance(
          userLocation.lat,
          userLocation.lng,
          place.location.lat,
          place.location.lng
        );
        
        // Add the distance to the place object for potential sorting
        place.distance = distanceToPlace;
        
        return distanceToPlace <= distance;
      });
    }

    // Filter by search query
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      filtered = filtered.filter(place => 
        place.name.toLowerCase().includes(query) || 
        (place.cuisine_type && place.cuisine_type.toLowerCase().includes(query))
      );
    }

    // Sort by distance if available
    if (userLocation) {
      filtered.sort((a, b) => (a.distance || 99) - (b.distance || 99));
    }

    setTimeout(() => {
      setFilteredPlaces(filtered);
      setIsLoading(false);
    }, 300); // Small timeout for UX
  }, [places, dietaryTags, religionTags, placeType, priceLevel, distance, userLocation, searchQuery]);

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="text-center mb-8">
        <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
          Find Food That Fits Your Lifestyle
        </h1>
        <p className="text-gray-600 max-w-2xl mx-auto">
          Discover restaurants and grocery stores that match your dietary preferences, 
          religious requirements, and location.
        </p>
      </div>

      <div className="mb-8">
        <SearchBar />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-12 gap-8">
        {/* Filters - Left Side */}
        <div className="lg:col-span-3">
          <FilterPanel />
        </div>

        {/* Main Content - Right Side */}
        <div className="lg:col-span-9">
          <div className="mb-8">
            <MapView 
              places={filteredPlaces}
              interactive={!!user}
            />
            {!user && (
              <div className="mt-2 text-sm text-center text-gray-500 flex items-center justify-center">
                <MapPin className="w-4 h-4 mr-1" />
                <span>Sign in to set your location and view the interactive map</span>
              </div>
            )}
          </div>
          <ResultsSection places={filteredPlaces} isLoading={isLoading} error={error} />
        </div>
      </div>
    </div>
  );
};

export default Home;