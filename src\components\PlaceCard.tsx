import React from 'react';
import { Link } from 'react-router-dom';
import { Utensils, ShoppingBag, DollarSign } from 'lucide-react';
import { Place } from '../types/place';

interface PlaceCardProps {
  place: Place;
}

const PlaceCard: React.FC<PlaceCardProps> = ({ place }) => {
  const renderPriceLevel = (level: number) => {
    return Array(level)
      .fill(0)
      .map((_, i) => <DollarSign key={i} className="w-4 h-4 inline" />);
  };

  // Ensure we have arrays even if the properties are undefined
  const dietaryTags = place.dietary_tags || [];
  const religiousTags = place.religious_tags || [];
  const totalTags = dietaryTags.length + religiousTags.length;

  return (
    <Link to={`/place/${place.id}`}>
      <div className="card overflow-hidden transition-all duration-300 hover:translate-y-[-4px]">
        <div className="h-40 bg-gray-200 relative">
          {place.image_url ? (
            <img 
              src={place.image_url} 
              alt={place.name} 
              className="h-full w-full object-cover"
            />
          ) : (
            <div className="h-full w-full flex items-center justify-center bg-gray-100">
              {place.type === 'restaurant' ? (
                <Utensils className="h-12 w-12 text-gray-400" />
              ) : (
                <ShoppingBag className="h-12 w-12 text-gray-400" />
              )}
            </div>
          )}
          <div className="absolute top-2 right-2">
            <div className={`badge ${place.type === 'restaurant' ? 'bg-secondary-100 text-secondary-800' : 'bg-primary-100 text-primary-800'}`}>
              {place.type === 'restaurant' ? 'Restaurant' : 'Grocery'}
            </div>
          </div>
        </div>

        <div className="p-4">
          <h3 className="font-semibold text-gray-800 mb-1">{place.name}</h3>
          
          <div className="mb-2 text-gray-600 text-sm flex items-center">
            <span>{place.cuisine_type || 'Various'}</span>
            <span className="mx-2">•</span>
            <span className="text-secondary-600">
              {renderPriceLevel(place.price_level)}
            </span>
          </div>

          <div className="flex flex-wrap gap-1 mt-2">
            {dietaryTags.slice(0, 3).map((tag) => (
              <span key={tag} className="badge-diet">{tag}</span>
            ))}
            {religiousTags.slice(0, 2).map((tag) => (
              <span key={tag} className="badge-religion">{tag}</span>
            ))}
            {totalTags > 5 && (
              <span className="badge bg-gray-100 text-gray-800">+{totalTags - 5} more</span>
            )}
          </div>
        </div>
      </div>
    </Link>
  );
};

export default PlaceCard;