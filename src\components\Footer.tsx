import React from 'react';
import { Heart } from 'lucide-react';

const Footer: React.FC = () => {
  return (
    <footer className="bg-gray-50 border-t border-gray-200 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="md:flex md:items-center md:justify-between">
          <div className="flex justify-center md:justify-start">
            <p className="text-gray-500 text-sm">
              © {new Date().getFullYear()} FilterFood. All rights reserved.
            </p>
          </div>
          
          <div className="mt-4 md:mt-0 flex justify-center md:justify-end">
            <p className="text-gray-500 text-sm flex items-center">
              Made with <Heart className="h-4 w-4 text-secondary-500 mx-1" /> for food lovers
            </p>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;