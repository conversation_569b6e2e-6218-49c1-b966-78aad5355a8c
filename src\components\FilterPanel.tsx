import React from 'react';
import { Filter, Utensils, ShoppingBag, Wallet } from 'lucide-react';
import { useFilterStore, DietaryTag, ReligionTag, PlaceType, PriceLevel, DistanceOption } from '../store/filterStore';

const dietaryOptions: { value: DietaryTag; label: string }[] = [
  { value: 'vegan', label: 'Vegan' },
  { value: 'vegetarian', label: 'Vegetarian' },
  { value: 'gluten-free', label: 'Gluten-Free' },
  { value: 'keto', label: 'Keto' },
  { value: 'paleo', label: 'Paleo' },
  { value: 'carnivore', label: 'Carnivore' },
  { value: 'dairy-free', label: 'Dairy-Free' },
];

const religionOptions: { value: ReligionTag; label: string }[] = [
  { value: 'halal', label: 'Halal' },
  { value: 'kosher', label: 'Kosher' },
  { value: 'lent-friendly', label: 'Lent-Friendly' },
];

const distanceOptions: { value: DistanceOption; label: string }[] = [
  { value: 1, label: '1km' },
  { value: 3, label: '3km' },
  { value: 5, label: '5km' },
  { value: 10, label: '10km' },
  { value: 25, label: '25km' },
];

const FilterPanel: React.FC = () => {
  const {
    dietaryTags,
    religionTags,
    placeType,
    priceLevel,
    distance,
    toggleDietaryTag,
    toggleReligionTag,
    setPlaceType,
    setPriceLevel,
    setDistance,
    resetFilters,
  } = useFilterStore();

  return (
    <div className="bg-white rounded-lg shadow-card p-4 md:p-6 animate-fade-in">
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold flex items-center">
          <Filter className="w-5 h-5 mr-2 text-primary-600" />
          Filters
        </h2>
        <button
          onClick={resetFilters}
          className="text-sm text-primary-600 hover:text-primary-700 font-medium"
        >
          Reset All
        </button>
      </div>

      {/* Place Type Filter */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-gray-700 mb-3">Place Type</h3>
        <div className="flex space-x-2">
          <button
            onClick={() => setPlaceType('all')}
            className={`flex-1 flex items-center justify-center px-3 py-2 rounded-md text-sm font-medium transition-colors
              ${placeType === 'all' ? 'bg-primary-100 text-primary-800' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
          >
            All
          </button>
          <button
            onClick={() => setPlaceType('restaurant')}
            className={`flex-1 flex items-center justify-center px-3 py-2 rounded-md text-sm font-medium transition-colors
              ${placeType === 'restaurant' ? 'bg-primary-100 text-primary-800' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
          >
            <Utensils className="w-4 h-4 mr-1" /> Restaurants
          </button>
          <button
            onClick={() => setPlaceType('grocery')}
            className={`flex-1 flex items-center justify-center px-3 py-2 rounded-md text-sm font-medium transition-colors
              ${placeType === 'grocery' ? 'bg-primary-100 text-primary-800' : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
          >
            <ShoppingBag className="w-4 h-4 mr-1" /> Grocery
          </button>
        </div>
      </div>

      {/* Dietary Preferences */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-gray-700 mb-3">Dietary Preferences</h3>
        <div className="flex flex-wrap gap-2">
          {dietaryOptions.map((option) => (
            <button
              key={option.value}
              onClick={() => toggleDietaryTag(option.value)}
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors
                ${dietaryTags.includes(option.value) 
                  ? 'bg-primary-100 text-primary-800' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>

      {/* Religious Preferences */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-gray-700 mb-3">Religious Preferences</h3>
        <div className="flex flex-wrap gap-2">
          {religionOptions.map((option) => (
            <button
              key={option.value}
              onClick={() => toggleReligionTag(option.value)}
              className={`px-3 py-1 rounded-full text-sm font-medium transition-colors
                ${religionTags.includes(option.value) 
                  ? 'bg-secondary-100 text-secondary-800' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>

      {/* Price Level */}
      <div className="mb-6">
        <h3 className="text-sm font-medium text-gray-700 mb-3 flex items-center">
          <Wallet className="w-4 h-4 mr-1" />
          Price Level
        </h3>
        <div className="flex space-x-2">
          {([1, 2, 3] as PriceLevel[]).map((level) => (
            <button
              key={level}
              onClick={() => setPriceLevel(priceLevel === level ? null : level)}
              className={`flex-1 px-3 py-2 rounded-md text-sm font-medium transition-colors
                ${priceLevel === level 
                  ? 'bg-gray-700 text-white' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
            >
              {Array(level).fill('$').join('')}
            </button>
          ))}
        </div>
      </div>

      {/* Distance */}
      <div>
        <h3 className="text-sm font-medium text-gray-700 mb-3">Distance</h3>
        <div className="grid grid-cols-3 gap-2">
          {distanceOptions.map((option) => (
            <button
              key={option.value}
              onClick={() => setDistance(option.value)}
              className={`px-2 py-2 rounded-md text-sm font-medium transition-colors
                ${distance === option.value 
                  ? 'bg-primary-100 text-primary-800' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
            >
              {option.label}
            </button>
          ))}
        </div>
      </div>
    </div>
  );
};

export default FilterPanel;