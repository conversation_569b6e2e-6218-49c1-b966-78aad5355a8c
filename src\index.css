@import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600;700&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  body {
    @apply bg-gray-50 text-gray-900 font-sans;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }
  
  h1 {
    @apply text-3xl font-bold text-gray-800 leading-tight;
  }
  
  h2 {
    @apply text-2xl font-semibold text-gray-800 leading-tight;
  }
  
  h3 {
    @apply text-xl font-semibold text-gray-800 leading-tight;
  }
}

@layer components {
  .btn {
    @apply px-4 py-2 rounded-md font-medium transition-all duration-200;
  }
  
  .btn-primary {
    @apply btn bg-primary-600 hover:bg-primary-700 text-white;
  }
  
  .btn-secondary {
    @apply btn bg-secondary-500 hover:bg-secondary-600 text-white;
  }
  
  .btn-outline {
    @apply btn border border-gray-300 hover:bg-gray-100 text-gray-700;
  }
  
  .input {
    @apply w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary-500 focus:border-transparent;
  }
  
  .card {
    @apply bg-white rounded-lg shadow-card hover:shadow-card-hover transition-shadow duration-300;
  }
  
  .badge {
    @apply inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium;
  }
  
  .badge-diet {
    @apply badge bg-primary-100 text-primary-800;
  }
  
  .badge-religion {
    @apply badge bg-secondary-100 text-secondary-800;
  }
  
  .badge-price {
    @apply badge bg-gray-100 text-gray-800;
  }
}

.mapboxgl-canvas {
  @apply rounded-lg;
}

.mapboxgl-popup {
  @apply max-w-xs;
}

.mapboxgl-popup-content {
  @apply p-4 rounded-lg shadow-lg bg-white;
}

/* Custom animations */
.animate-fade-in {
  animation: fadeIn 0.5s ease-in-out;
}

.animate-slide-up {
  animation: slideUp 0.4s ease-out;
}

@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% { transform: translateY(20px); opacity: 0; }
  100% { transform: translateY(0); opacity: 1; }
}