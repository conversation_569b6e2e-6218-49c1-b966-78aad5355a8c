import React, { useEffect, useRef, useState } from 'react';
import mapboxgl from 'mapbox-gl';
import 'mapbox-gl/dist/mapbox-gl.css';
import { MapPin } from 'lucide-react';
import { useFilterStore } from '../store/filterStore';
import { Place } from '../types/place';

// Get the Mapbox token from environment variables
const mapboxToken = import.meta.env.VITE_MAPBOX_ACCESS_TOKEN;

// Only set the token if it's available
if (mapboxToken) {
  mapboxgl.accessToken = mapboxToken;
} else {
  console.error('Mapbox access token is not set. Please check your .env file.');
}

interface MapViewProps {
  places?: Place[];
  height?: string;
  interactive?: boolean;
  singlePlace?: Place;
  className?: string;
}

const MapView: React.FC<MapViewProps> = ({ 
  places, 
  height = 'h-[300px] sm:h-[400px]', 
  interactive = true,
  singlePlace,
  className = ''
}) => {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<mapboxgl.Map | null>(null);
  const [mapLoaded, setMapLoaded] = useState(false);
  const markers = useRef<mapboxgl.Marker[]>([]);
  const circleRadius = useRef<mapboxgl.Marker | null>(null);
  
  const { userLocation, setUserLocation, distance } = useFilterStore();
  const defaultLocation = { lat: 40.7128, lng: -74.0060 }; // New York as default

  // Initialize map
  useEffect(() => {
    if (!mapContainer.current) return;
    if (map.current) return;
    if (!mapboxToken) {
      console.error('Cannot initialize map without Mapbox access token');
      return;
    }

    const initialLocation = userLocation || 
                           (singlePlace ? { lat: singlePlace.location.lat, lng: singlePlace.location.lng } : 
                           defaultLocation);

    map.current = new mapboxgl.Map({
      container: mapContainer.current,
      style: 'mapbox://styles/mapbox/streets-v12',
      center: [initialLocation.lng, initialLocation.lat],
      zoom: singlePlace ? 14 : 11,
    });

    map.current.on('load', () => {
      setMapLoaded(true);
      
      // Add user location marker if not in single place view
      if (!singlePlace && interactive) {
        // Try to get user's location
        navigator.geolocation.getCurrentPosition(
          (position) => {
            const userLoc = {
              lat: position.coords.latitude,
              lng: position.coords.longitude
            };
            setUserLocation(userLoc);
            
            if (map.current) {
              map.current.flyTo({
                center: [userLoc.lng, userLoc.lat],
                zoom: 12,
                essential: true
              });
            }
          },
          (error) => {
            console.error('Error getting location:', error);
          }
        );
      }
    });

    // Clean up on unmount
    return () => {
      if (map.current) {
        map.current.remove();
        map.current = null;
      }
      markers.current.forEach(marker => marker.remove());
      markers.current = [];
      if (circleRadius.current) {
        circleRadius.current.remove();
        circleRadius.current = null;
      }
    };
  }, []); // Empty deps - only run once on mount

  // Add user location marker and make it draggable
  useEffect(() => {
    if (!mapLoaded || !map.current || !interactive || singlePlace) return;

    // Clear existing user marker
    if (circleRadius.current) {
      circleRadius.current.remove();
      circleRadius.current = null;
    }

    if (!userLocation) return;

    // Add user marker
    const el = document.createElement('div');
    el.className = 'flex items-center justify-center';
    el.innerHTML = `
      <div class="text-primary-600">
        <svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path>
          <circle cx="12" cy="10" r="3"></circle>
        </svg>
      </div>
    `;

    const marker = new mapboxgl.Marker({
      element: el,
      draggable: interactive
    })
      .setLngLat([userLocation.lng, userLocation.lat])
      .addTo(map.current);
    
    // Update location when marker is dragged
    marker.on('dragend', () => {
      const lngLat = marker.getLngLat();
      setUserLocation({ lat: lngLat.lat, lng: lngLat.lng });
    });

    circleRadius.current = marker;

    // Add search radius circle
    if (distance && map.current.getSource('radius') === undefined) {
      map.current.addSource('radius', {
        type: 'geojson',
        data: {
          type: 'Feature',
          geometry: {
            type: 'Point',
            coordinates: [userLocation.lng, userLocation.lat]
          },
          properties: {
            radius: distance * 1000 // convert km to meters
          }
        }
      });

      map.current.addLayer({
        id: 'radius-fill',
        type: 'circle',
        source: 'radius',
        paint: {
          'circle-radius': ['get', 'radius'],
          'circle-color': 'rgba(47, 133, 90, 0.1)',
          'circle-stroke-width': 2,
          'circle-stroke-color': 'rgba(47, 133, 90, 0.6)'
        }
      });
    } else if (distance && map.current.getSource('radius')) {
      // Update existing radius
      map.current.getSource('radius').setData({
        type: 'Feature',
        geometry: {
          type: 'Point',
          coordinates: [userLocation.lng, userLocation.lat]
        },
        properties: {
          radius: distance * 1000 // convert km to meters
        }
      });
    }

    return () => {
      if (circleRadius.current) {
        circleRadius.current.remove();
        circleRadius.current = null;
      }
    };
  }, [mapLoaded, userLocation, interactive, distance, singlePlace]);

  // Update radius when distance changes
  useEffect(() => {
    if (!mapLoaded || !map.current || !userLocation) return;
    
    if (map.current.getSource('radius')) {
      map.current.getSource('radius').setData({
        type: 'Feature',
        geometry: {
          type: 'Point',
          coordinates: [userLocation.lng, userLocation.lat]
        },
        properties: {
          radius: (distance || 5) * 1000 // convert km to meters, default to 5km
        }
      });
    }
  }, [distance, mapLoaded, userLocation]);

  // Add place markers
  useEffect(() => {
    if (!mapLoaded || !map.current) return;
    
    // Clear existing place markers
    markers.current.forEach(marker => marker.remove());
    markers.current = [];

    // For single place view
    if (singlePlace) {
      const el = document.createElement('div');
      el.className = 'flex items-center justify-center';
      el.innerHTML = `
        <div class="text-secondary-500">
          <svg width="36" height="36" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M20 10c0 6-8 12-8 12s-8-6-8-12a8 8 0 0 1 16 0Z"></path>
            <circle cx="12" cy="10" r="3"></circle>
          </svg>
        </div>
      `;

      const marker = new mapboxgl.Marker({ element: el })
        .setLngLat([singlePlace.location.lng, singlePlace.location.lat])
        .addTo(map.current);
      
      markers.current.push(marker);

      // Center map on the single place
      map.current.flyTo({
        center: [singlePlace.location.lng, singlePlace.location.lat],
        zoom: 14,
        essential: true
      });

      return;
    }

    // Add markers for all places
    if (places && places.length > 0) {
      places.forEach(place => {
        const el = document.createElement('div');
        el.className = 'cursor-pointer';
        
        // Different icon based on place type
        const color = place.type === 'restaurant' ? 'text-secondary-500' : 'text-primary-600';
        el.innerHTML = `
          <div class="${color}">
            ${place.type === 'restaurant' ? 
              `<svg width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M7 21h10"></path>
                <path d="M12 21a9 9 0 0 0 9-9H3a9 9 0 0 0 9 9Z"></path>
                <path d="M11.38 12a2.4 2.4 0 0 1-.4-4.77 2.4 2.4 0 0 1 3.2-2.77 2.4 2.4 0 0 1 3.47-.63 2.4 2.4 0 0 1 3.37 3.37 2.4 2.4 0 0 1-1.1 3.7 2.51 2.51 0 0 1 .03 1.1"></path>
                <path d="m13 12 4-4"></path>
                <path d="M10.9 7.25A3.99 3.99 0 0 0 4 10c0 .73.2 1.41.54 2"></path>
              </svg>` 
            : 
              `<svg width="28" height="28" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z"></path>
                <path d="M3 6h18"></path>
                <path d="M16 10a4 4 0 0 1-8 0"></path>
              </svg>`
            }
          </div>
        `;

        const popup = new mapboxgl.Popup({ offset: 25 })
          .setHTML(`
            <div class="font-medium">${place.name}</div>
            <div class="text-sm text-gray-500">${place.type === 'restaurant' ? 'Restaurant' : 'Grocery'}</div>
          `);

        const marker = new mapboxgl.Marker({ element: el })
          .setLngLat([place.location.lng, place.location.lat])
          .setPopup(popup)
          .addTo(map.current);
        
        el.addEventListener('click', () => {
          window.location.href = `/place/${place.id}`;
        });

        markers.current.push(marker);
      });

      // If we have places but no user location set, let's fit the map to show all places
      if (!userLocation && places.length > 0 && map.current) {
        const bounds = new mapboxgl.LngLatBounds();
        places.forEach(place => {
          bounds.extend([place.location.lng, place.location.lat]);
        });
        
        map.current.fitBounds(bounds, {
          padding: 50,
          maxZoom: 14
        });
      }
    }
  }, [mapLoaded, places, singlePlace, userLocation]);

  return (
    <div className={`rounded-lg overflow-hidden relative ${height} ${className}`}>
      {!mapboxToken && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40 z-10 text-white">
          <span className="font-medium">Mapbox token not configured</span>
        </div>
      )}
      {!interactive && !singlePlace && (
        <div className="absolute inset-0 flex items-center justify-center bg-black bg-opacity-40 z-10 text-white">
          <MapPin className="w-6 h-6 mr-2" />
          <span className="font-medium">Login to view the map</span>
        </div>
      )}
      <div ref={mapContainer} className="h-full w-full" />
    </div>
  );
};

export default MapView;