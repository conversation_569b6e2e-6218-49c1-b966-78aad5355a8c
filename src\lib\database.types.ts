export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      places: {
        Row: {
          id: number
          created_at: string
          name: string
          type: 'restaurant' | 'grocery'
          location: { lat: number; lng: number }
          address: string
          cuisine_type: string | null
          dietary_tags: string[]
          religion_tags: string[]
          price_level: 1 | 2 | 3
          description: string | null
          image_url: string | null
        }
        Insert: {
          id?: number
          created_at?: string
          name: string
          type: 'restaurant' | 'grocery'
          location: { lat: number; lng: number }
          address: string
          cuisine_type?: string | null
          dietary_tags?: string[]
          religion_tags?: string[]
          price_level: 1 | 2 | 3
          description?: string | null
          image_url?: string | null
        }
        Update: {
          id?: number
          created_at?: string
          name?: string
          type?: 'restaurant' | 'grocery'
          location?: { lat: number; lng: number }
          address?: string
          cuisine_type?: string | null
          dietary_tags?: string[]
          religion_tags?: string[]
          price_level?: 1 | 2 | 3
          description?: string | null
          image_url?: string | null
        }
      }
      profiles: {
        Row: {
          id: string
          created_at: string
          updated_at: string
          full_name: string | null
          avatar_url: string | null
          dietary_preferences: string[]
          religious_preferences: string[]
          favorite_places: number[]
        }
        Insert: {
          id: string
          created_at?: string
          updated_at?: string
          full_name?: string | null
          avatar_url?: string | null
          dietary_preferences?: string[]
          religious_preferences?: string[]
          favorite_places?: number[]
        }
        Update: {
          id?: string
          created_at?: string
          updated_at?: string
          full_name?: string | null
          avatar_url?: string | null
          dietary_preferences?: string[]
          religious_preferences?: string[]
          favorite_places?: number[]
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}