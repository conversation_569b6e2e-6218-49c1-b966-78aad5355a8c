import { create } from 'zustand';

export type DietaryTag = 'vegan' | 'vegetarian' | 'gluten-free' | 'keto' | 'carnivore' | 'paleo' | 'dairy-free';
export type ReligionTag = 'halal' | 'kosher' | 'lent-friendly';
export type PlaceType = 'restaurant' | 'grocery' | 'all';
export type PriceLevel = 1 | 2 | 3 | null;
export type DistanceOption = 1 | 3 | 5 | 10 | 25 | null;

export interface FilterState {
  dietaryTags: DietaryTag[];
  religionTags: ReligionTag[];
  placeType: PlaceType;
  priceLevel: PriceLevel;
  distance: DistanceOption;
  userLocation: { lat: number; lng: number } | null;
  searchQuery: string;

  // Actions
  toggleDietaryTag: (tag: DietaryTag) => void;
  toggleReligionTag: (tag: ReligionTag) => void;
  setPlaceType: (type: PlaceType) => void;
  setPriceLevel: (level: PriceLevel) => void;
  setDistance: (distance: DistanceOption) => void;
  setUserLocation: (location: { lat: number; lng: number } | null) => void;
  setSearchQuery: (query: string) => void;
  resetFilters: () => void;
}

export const useFilterStore = create<FilterState>((set) => ({
  dietaryTags: [],
  religionTags: [],
  placeType: 'all',
  priceLevel: null,
  distance: 5,
  userLocation: null,
  searchQuery: '',

  toggleDietaryTag: (tag) => 
    set((state) => ({
      dietaryTags: state.dietaryTags.includes(tag)
        ? state.dietaryTags.filter((t) => t !== tag)
        : [...state.dietaryTags, tag],
    })),

  toggleReligionTag: (tag) => 
    set((state) => ({
      religionTags: state.religionTags.includes(tag)
        ? state.religionTags.filter((t) => t !== tag)
        : [...state.religionTags, tag],
    })),

  setPlaceType: (type) => set({ placeType: type }),
  setPriceLevel: (level) => set({ priceLevel: level }),
  setDistance: (distance) => set({ distance: distance }),
  setUserLocation: (location) => set({ userLocation: location }),
  setSearchQuery: (query) => set({ searchQuery: query }),
  
  resetFilters: () => set({
    dietaryTags: [],
    religionTags: [],
    placeType: 'all',
    priceLevel: null,
    distance: 5,
    searchQuery: '',
  }),
}));