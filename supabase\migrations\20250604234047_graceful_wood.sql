/*
  # Seed data for places table

  1. Sample Data
    - Add 12 sample places (6 restaurants and 6 grocery stores)
    - Include a mix of dietary and religious options
    - Set different price levels
    - Provide sample locations (centered around NYC)
*/

-- Sample restaurants
INSERT INTO places (name, type, location, address, cuisine_type, dietary_tags, religion_tags, price_level, description, image_url)
VALUES
  (
    'Green Garden Bistro', 
    'restaurant',
    '{"lat": 40.7128, "lng": -74.0060}',
    '123 Broadway, New York, NY 10007',
    'Vegetarian',
    ARRAY['vegan', 'vegetarian', 'gluten-free'],
    ARRAY['halal', 'kosher'],
    2,
    'A plant-based paradise offering creative, seasonal dishes in a warm, eco-friendly atmosphere.',
    'https://images.pexels.com/photos/260922/pexels-photo-260922.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
  ),
  (
    'Halal Grill House', 
    'restaurant',
    '{"lat": 40.7282, "lng": -73.9882}',
    '456 5th Avenue, New York, NY 10016',
    'Mediterranean',
    ARRAY['carnivore', 'paleo'],
    ARRAY['halal'],
    2,
    'Authentic Mediterranean cuisine with halal-certified meats, offering shawarma, kebabs, and falafel.',
    'https://images.pexels.com/photos/5490876/pexels-photo-5490876.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
  ),
  (
    'Kosher Delight', 
    'restaurant',
    '{"lat": 40.7589, "lng": -73.9851}',
    '789 Madison Ave, New York, NY 10065',
    'Deli',
    ARRAY['carnivore'],
    ARRAY['kosher'],
    2,
    'Traditional kosher delicatessen with house-made pastrami, matzo ball soup, and fresh-baked breads.',
    'https://images.pexels.com/photos/262978/pexels-photo-262978.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
  ),
  (
    'Keto Kitchen', 
    'restaurant',
    '{"lat": 40.7112, "lng": -74.0123}',
    '101 Hudson St, New York, NY 10013',
    'American',
    ARRAY['keto', 'carnivore', 'gluten-free'],
    ARRAY['lent-friendly'],
    3,
    'Specializing in low-carb, high-fat meals for keto lifestyle enthusiasts without compromising on flavor.',
    'https://images.pexels.com/photos/67468/pexels-photo-67468.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
  ),
  (
    'Pure Vegan Cafe', 
    'restaurant',
    '{"lat": 40.7234, "lng": -73.9875}',
    '222 E 14th St, New York, NY 10003',
    'Vegan',
    ARRAY['vegan', 'gluten-free', 'dairy-free'],
    ARRAY['halal', 'kosher'],
    1,
    'Cozy cafe with 100% plant-based menu featuring global comfort foods and decadent desserts.',
    'https://images.pexels.com/photos/1640774/pexels-photo-1640774.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
  ),
  (
    'Lenten Fish Grill', 
    'restaurant',
    '{"lat": 40.7412, "lng": -73.9974}',
    '333 W 23rd St, New York, NY 10011',
    'Seafood',
    ARRAY['gluten-free', 'dairy-free'],
    ARRAY['lent-friendly'],
    3,
    'Specializing in seafood dishes perfect for the Lenten season, with sustainably-sourced fish and shellfish.',
    'https://images.pexels.com/photos/262959/pexels-photo-262959.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
  ),

  -- Sample grocery stores
  (
    'Organic Market', 
    'grocery',
    '{"lat": 40.7354, "lng": -73.9912}',
    '444 Park Ave, New York, NY 10016',
    NULL,
    ARRAY['vegan', 'vegetarian', 'gluten-free', 'organic'],
    ARRAY['halal', 'kosher'],
    2,
    'Full-service organic grocery store with extensive vegan and gluten-free options.',
    'https://images.pexels.com/photos/2733918/pexels-photo-2733918.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
  ),
  (
    'Halal Meats & More', 
    'grocery',
    '{"lat": 40.7483, "lng": -73.9729}',
    '555 Lexington Ave, New York, NY 10022',
    NULL,
    ARRAY['carnivore'],
    ARRAY['halal'],
    1,
    'Specializing in halal-certified meats, poultry, and imported Middle Eastern groceries.',
    'https://images.pexels.com/photos/1036857/pexels-photo-1036857.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
  ),
  (
    'Kosher Pantry', 
    'grocery',
    '{"lat": 40.7731, "lng": -73.9822}',
    '666 3rd Ave, New York, NY 10017',
    NULL,
    ARRAY['gluten-free', 'vegetarian'],
    ARRAY['kosher'],
    2,
    'Your one-stop shop for all kosher needs, from everyday essentials to holiday specialties.',
    'https://images.pexels.com/photos/1132558/pexels-photo-1132558.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
  ),
  (
    'Low Carb Essentials', 
    'grocery',
    '{"lat": 40.7168, "lng": -74.0465}',
    '777 Washington St, New York, NY 10014',
    NULL,
    ARRAY['keto', 'paleo', 'carnivore'],
    ARRAY[]::text[],
    3,
    'Specialized grocery store featuring low-carb and keto-friendly foods, supplements, and snacks.',
    'https://images.pexels.com/photos/264636/pexels-photo-264636.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
  ),
  (
    'Vegan Valley', 
    'grocery',
    '{"lat": 40.7281, "lng": -73.9467}',
    '888 Bedford Ave, Brooklyn, NY 11205',
    NULL,
    ARRAY['vegan', 'vegetarian', 'dairy-free'],
    ARRAY['halal', 'kosher', 'lent-friendly'],
    1,
    'Plant-based grocery store offering a wide range of vegan alternatives, produce, and bulk foods.',
    'https://images.pexels.com/photos/1392585/pexels-photo-1392585.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
  ),
  (
    'Faith Foods', 
    'grocery',
    '{"lat": 40.7489, "lng": -73.9680}',
    '999 2nd Ave, New York, NY 10017',
    NULL,
    ARRAY['vegetarian', 'gluten-free'],
    ARRAY['halal', 'kosher', 'lent-friendly'],
    2,
    'Grocery store specializing in foods that meet various religious dietary requirements.',
    'https://images.pexels.com/photos/3962294/pexels-photo-3962294.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=1'
  );