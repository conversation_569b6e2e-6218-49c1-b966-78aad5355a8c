import React, { useState, useEffect } from 'react';
import { AlertCircle, CheckCircle, Info, X } from 'lucide-react';

type ToastType = 'success' | 'error' | 'info';

interface Toast {
  id: string;
  message: string;
  type: ToastType;
}

export const toastState = {
  toasts: [] as Toast[],
  addToast: (message: string, type: ToastType = 'info') => {
    const id = Math.random().toString(36).substring(2, 9);
    const newToast = { id, message, type };
    toastState.toasts.push(newToast);
    document.dispatchEvent(new CustomEvent('toast-added', { detail: newToast }));
    
    setTimeout(() => {
      toastState.removeToast(id);
    }, 3000);
  },
  removeToast: (id: string) => {
    const index = toastState.toasts.findIndex(toast => toast.id === id);
    if (index !== -1) {
      toastState.toasts.splice(index, 1);
      document.dispatchEvent(new CustomEvent('toast-removed', { detail: { id } }));
    }
  }
};

export const Toaster: React.FC = () => {
  const [toasts, setToasts] = useState<Toast[]>([]);

  useEffect(() => {
    const handleToastAdded = (e: CustomEvent<Toast>) => {
      setToasts(prev => [...prev, e.detail]);
    };

    const handleToastRemoved = (e: CustomEvent<{ id: string }>) => {
      setToasts(prev => prev.filter(toast => toast.id !== e.detail.id));
    };

    document.addEventListener('toast-added', handleToastAdded as EventListener);
    document.addEventListener('toast-removed', handleToastRemoved as EventListener);

    return () => {
      document.removeEventListener('toast-added', handleToastAdded as EventListener);
      document.removeEventListener('toast-removed', handleToastRemoved as EventListener);
    };
  }, []);

  const getToastIcon = (type: ToastType) => {
    switch (type) {
      case 'success':
        return <CheckCircle className="w-5 h-5 text-green-500" />;
      case 'error':
        return <AlertCircle className="w-5 h-5 text-red-500" />;
      case 'info':
      default:
        return <Info className="w-5 h-5 text-blue-500" />;
    }
  };

  const getToastClasses = (type: ToastType) => {
    switch (type) {
      case 'success':
        return 'bg-green-50 border-green-200 text-green-800';
      case 'error':
        return 'bg-red-50 border-red-200 text-red-800';
      case 'info':
      default:
        return 'bg-blue-50 border-blue-200 text-blue-800';
    }
  };

  return (
    <div className="fixed bottom-4 right-4 z-50 flex flex-col space-y-2 max-w-xs w-full">
      {toasts.map((toast) => (
        <div
          key={toast.id}
          className={`p-3 rounded-md shadow-md border animate-slide-up flex items-center justify-between ${getToastClasses(toast.type)}`}
        >
          <div className="flex items-center space-x-2">
            {getToastIcon(toast.type)}
            <span>{toast.message}</span>
          </div>
          <button 
            onClick={() => toastState.removeToast(toast.id)}
            className="text-gray-500 hover:text-gray-700"
          >
            <X className="w-4 h-4" />
          </button>
        </div>
      ))}
    </div>
  );
};