import React, { useState } from 'react';
import { Search } from 'lucide-react';
import { useFilterStore } from '../store/filterStore';

const SearchBar: React.FC = () => {
  const [inputValue, setInputValue] = useState('');
  const { setSearchQuery } = useFilterStore();

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    setSearchQuery(inputValue);
  };

  return (
    <form onSubmit={handleSubmit} className="relative w-full">
      <div className="relative">
        <input
          type="text"
          placeholder="Search for restaurants or grocery stores..."
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          className="input pr-12"
        />
        <button
          type="submit"
          className="absolute right-0 top-0 h-full px-3 flex items-center justify-center text-gray-500 hover:text-primary-600"
        >
          <Search className="h-5 w-5" />
        </button>
      </div>
    </form>
  );
};

export default SearchBar;