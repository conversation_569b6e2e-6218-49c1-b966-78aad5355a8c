/*
  # Create places and profiles tables

  1. New Tables
    - `places`: Stores information about restaurants and grocery stores
      - `id` (primary key)
      - `name` (text)
      - `type` (text, 'restaurant' or 'grocery')
      - `location` (jsonb, latitude and longitude)
      - `address` (text)
      - `cuisine_type` (text)
      - `dietary_tags` (text array)
      - `religion_tags` (text array)
      - `price_level` (smallint)
      - `description` (text)
      - `image_url` (text)
    
    - `profiles`: Stores user profiles with preferences
      - `id` (uuid, references auth.users)
      - `full_name` (text)
      - `avatar_url` (text)
      - `dietary_preferences` (text array)
      - `religious_preferences` (text array)
      - `favorite_places` (integer array)

  2. Security
    - Enable RLS on all tables
    - Set up appropriate policies for each table
*/

-- Create places table
CREATE TABLE IF NOT EXISTS places (
  id SERIAL PRIMARY KEY,
  created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
  name TEXT NOT NULL,
  type TEXT NOT NULL CHECK (type IN ('restaurant', 'grocery')),
  location JSONB NOT NULL,
  address TEXT NOT NULL,
  cuisine_type TEXT,
  dietary_tags TEXT[] DEFAULT '{}' NOT NULL,
  religion_tags TEXT[] DEFAULT '{}' NOT NULL,
  price_level SMALLINT NOT NULL CHECK (price_level BETWEEN 1 AND 3),
  description TEXT,
  image_url TEXT
);

-- Create profiles table that connects to auth.users
CREATE TABLE IF NOT EXISTS profiles (
  id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
  created_at TIMESTAMPTZ DEFAULT now() NOT NULL,
  updated_at TIMESTAMPTZ DEFAULT now() NOT NULL,
  full_name TEXT,
  avatar_url TEXT,
  dietary_preferences TEXT[] DEFAULT '{}' NOT NULL,
  religious_preferences TEXT[] DEFAULT '{}' NOT NULL,
  favorite_places INTEGER[] DEFAULT '{}' NOT NULL
);

-- Enable Row Level Security
ALTER TABLE places ENABLE ROW LEVEL SECURITY;
ALTER TABLE profiles ENABLE ROW LEVEL SECURITY;

-- Create policies for places table
-- Anyone can read places
CREATE POLICY "Anyone can read places"
  ON places
  FOR SELECT
  TO anon, authenticated
  USING (true);

-- Only admin can create/update/delete places (in production, you'd use a more secure approach)
CREATE POLICY "Admin users can modify places"
  ON places
  USING (auth.uid() IN (
    SELECT auth.uid() FROM auth.users WHERE email = '<EMAIL>'
  ));

-- Create policies for profiles table
-- Users can read their own profile
CREATE POLICY "Users can read own profile"
  ON profiles
  FOR SELECT
  TO authenticated
  USING (auth.uid() = id);

-- Users can update their own profile
CREATE POLICY "Users can update own profile"
  ON profiles
  FOR UPDATE
  TO authenticated
  USING (auth.uid() = id);

-- Create a trigger to create a profile when a new user signs up
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.profiles (id)
  VALUES (new.id);
  RETURN new;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Set up the trigger on auth.users
CREATE TRIGGER create_profile_for_user
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE PROCEDURE public.handle_new_user();