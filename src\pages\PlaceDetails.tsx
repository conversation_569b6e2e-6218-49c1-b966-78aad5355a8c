import React, { useState, useEffect } from 'react';
import { use<PERSON><PERSON><PERSON>, Link } from 'react-router-dom';
import { 
  ArrowLeft, 
  MapPin, 
  Utensils, 
  ShoppingBag, 
  DollarSign,
  Heart
} from 'lucide-react';
import { supabase } from '../lib/supabase';
import { Place } from '../types/place';
import MapView from '../components/MapView';
import { useAuth } from '../context/AuthContext';
import { toastState } from '../components/ui/Toaster';

const PlaceDetails: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const [place, setPlace] = useState<Place | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isFavorite, setIsFavorite] = useState(false);
  
  const { user } = useAuth();

  // Fetch place details from Supabase
  useEffect(() => {
    const fetchPlace = async () => {
      if (!id) return;
      
      setIsLoading(true);
      setError(null);

      try {
        // Fetch place data
        const { data, error } = await supabase
          .from('places')
          .select('*')
          .eq('id', id)
          .single();

        if (error) {
          throw new Error(error.message);
        }

        setPlace(data as Place);
        
        // Check if place is in user's favorites if logged in
        if (user) {
          const { data: profileData, error: profileError } = await supabase
            .from('profiles')
            .select('favorite_places')
            .eq('id', user.id)
            .single();
            
          if (!profileError && profileData && profileData.favorite_places) {
            setIsFavorite(profileData.favorite_places.includes(parseInt(id)));
          }
        }
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An unknown error occurred');
        console.error('Error fetching place:', err);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPlace();
  }, [id, user]);

  // Toggle favorite status
  const toggleFavorite = async () => {
    if (!user || !place) {
      toastState.addToast('Please sign in to save favorites', 'info');
      return;
    }

    try {
      // Get current favorites
      const { data, error } = await supabase
        .from('profiles')
        .select('favorite_places')
        .eq('id', user.id)
        .single();
        
      if (error) {
        throw new Error(error.message);
      }
      
      let favorites = data?.favorite_places || [];
      
      // Toggle the place in favorites
      if (isFavorite) {
        favorites = favorites.filter((placeId: number) => placeId !== place.id);
      } else {
        favorites.push(place.id);
      }
      
      // Update in database
      const { error: updateError } = await supabase
        .from('profiles')
        .update({ favorite_places: favorites })
        .eq('id', user.id);
        
      if (updateError) {
        throw new Error(updateError.message);
      }
      
      setIsFavorite(!isFavorite);
      toastState.addToast(
        isFavorite ? 'Removed from favorites' : 'Added to favorites', 
        'success'
      );
    } catch (err) {
      console.error('Error updating favorites:', err);
      toastState.addToast('Failed to update favorites', 'error');
    }
  };

  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (error || !place) {
    return (
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="text-center">
          <h2 className="text-2xl font-bold text-gray-900 mb-4">
            Error loading place details
          </h2>
          <p className="text-gray-600 mb-6">{error || 'Place not found'}</p>
          <Link to="/" className="btn-primary">
            Go back to search
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="mb-6">
        <Link to="/" className="inline-flex items-center text-primary-600 hover:text-primary-700">
          <ArrowLeft className="h-4 w-4 mr-1" />
          Back to results
        </Link>
      </div>

      <div className="bg-white shadow-card rounded-lg overflow-hidden">
        {/* Header image */}
        <div className="h-56 md:h-72 bg-gray-200 relative">
          {place.image_url ? (
            <img 
              src={place.image_url} 
              alt={place.name} 
              className="h-full w-full object-cover"
            />
          ) : (
            <div className="h-full w-full flex items-center justify-center bg-gray-100">
              {place.type === 'restaurant' ? (
                <Utensils className="h-16 w-16 text-gray-400" />
              ) : (
                <ShoppingBag className="h-16 w-16 text-gray-400" />
              )}
            </div>
          )}
          
          <div className="absolute top-4 right-4 flex space-x-2">
            <div className={`badge ${
              place.type === 'restaurant' 
                ? 'bg-secondary-100 text-secondary-800' 
                : 'bg-primary-100 text-primary-800'
            }`}>
              {place.type === 'restaurant' ? 'Restaurant' : 'Grocery'}
            </div>
            
            {user && (
              <button 
                onClick={toggleFavorite}
                className={`p-2 rounded-full ${
                  isFavorite 
                    ? 'bg-secondary-100 text-secondary-700' 
                    : 'bg-gray-100 text-gray-500 hover:bg-gray-200'
                }`}
              >
                <Heart className={`h-5 w-5 ${isFavorite ? 'fill-current' : ''}`} />
              </button>
            )}
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="flex flex-wrap justify-between items-start mb-4">
            <div>
              <h1 className="text-2xl md:text-3xl font-bold text-gray-900">{place.name}</h1>
              
              <div className="flex items-center text-gray-600 mt-2">
                <span className="mr-3">{place.cuisine_type || 'Various'}</span>
                <span className="flex">
                  {Array(place.price_level).fill(0).map((_, i) => (
                    <DollarSign key={i} className="w-4 h-4 text-secondary-600" />
                  ))}
                </span>
              </div>
            </div>
          </div>

          {/* Address */}
          <div className="flex items-start mb-6">
            <MapPin className="h-5 w-5 text-gray-500 mr-2 mt-0.5" />
            <span className="text-gray-700">{place.address}</span>
          </div>

          {/* Tags */}
          <div className="mb-6">
            <h3 className="font-medium text-gray-900 mb-2">Features</h3>
            <div className="flex flex-wrap gap-2">
              {place.dietary_tags.map((tag) => (
                <span key={tag} className="badge-diet">{tag}</span>
              ))}
              {place.religious_tags.map((tag) => (
                <span key={tag} className="badge-religion">{tag}</span>
              ))}
            </div>
          </div>

          {/* Description */}
          {place.description && (
            <div className="mb-8">
              <h3 className="font-medium text-gray-900 mb-2">About</h3>
              <p className="text-gray-700">{place.description}</p>
            </div>
          )}

          {/* Map */}
          <div>
            <h3 className="font-medium text-gray-900 mb-2">Location</h3>
            <MapView 
              singlePlace={place} 
              height="h-[300px]" 
            />
          </div>
        </div>
      </div>
    </div>
  );
};

export default PlaceDetails;