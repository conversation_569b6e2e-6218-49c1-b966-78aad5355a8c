import React from 'react';
import { SortAsc, Search } from 'lucide-react';
import PlaceCard from './PlaceCard';
import { Place } from '../types/place';

interface ResultsSectionProps {
  places: Place[];
  isLoading: boolean;
  error: string | null;
}

const ResultsSection: React.FC<ResultsSectionProps> = ({ places, isLoading, error }) => {
  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600 mb-4"></div>
        <p className="text-gray-600">Finding places that match your preferences...</p>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <div className="bg-red-100 text-red-800 p-4 rounded-lg max-w-md">
          <p>Sorry, there was an error loading results: {error}</p>
        </div>
      </div>
    );
  }

  if (places.length === 0) {
    return (
      <div className="flex flex-col items-center justify-center py-12 text-center">
        <Search className="h-12 w-12 text-gray-400 mb-4" />
        <h3 className="text-xl font-medium text-gray-700 mb-2">No matching places found</h3>
        <p className="text-gray-500 max-w-md">
          Try adjusting your filters or expanding your search area to discover more options.
        </p>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-4">
        <h2 className="text-lg font-semibold text-gray-800">Results</h2>
        <div className="flex items-center">
          <span className="text-gray-500 text-sm mr-2">{places.length} places found</span>
          <button className="btn-outline text-sm py-1 px-2 flex items-center">
            <SortAsc className="h-4 w-4 mr-1" />
            Sort
          </button>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {places.map((place) => (
          <PlaceCard key={place.id} place={place} />
        ))}
      </div>
    </div>
  );
};

export default ResultsSection;