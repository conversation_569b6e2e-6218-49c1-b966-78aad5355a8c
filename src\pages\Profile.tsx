import React, { useState, useEffect } from 'react';
import { User, Settings, LogOut } from 'lucide-react';
import { useAuth } from '../context/AuthContext';
import { supabase } from '../lib/supabase';
import { useNavigate } from 'react-router-dom';
import { DietaryTag, ReligionTag } from '../store/filterStore';
import { Place } from '../types/place';
import PlaceCard from '../components/PlaceCard';
import { toastState } from '../components/ui/Toaster';

const dietaryOptions: { value: DietaryTag; label: string }[] = [
  { value: 'vegan', label: 'Vegan' },
  { value: 'vegetarian', label: 'Vegetarian' },
  { value: 'gluten-free', label: 'Gluten-Free' },
  { value: 'keto', label: 'Keto' },
  { value: 'paleo', label: 'Paleo' },
  { value: 'carnivore', label: 'Carnivore' },
  { value: 'dairy-free', label: 'Dairy-Free' },
];

const religionOptions: { value: ReligionTag; label: string }[] = [
  { value: 'halal', label: 'Halal' },
  { value: 'kosher', label: 'Kosher' },
  { value: 'lent-friendly', label: 'Lent-Friendly' },
];

interface Profile {
  id: string;
  full_name: string | null;
  avatar_url: string | null;
  dietary_preferences: DietaryTag[];
  religious_preferences: ReligionTag[];
  favorite_places: number[];
}

const Profile: React.FC = () => {
  const { user, signOut } = useAuth();
  const navigate = useNavigate();
  
  const [profile, setProfile] = useState<Profile | null>(null);
  const [name, setName] = useState('');
  const [dietaryPreferences, setDietaryPreferences] = useState<DietaryTag[]>([]);
  const [religiousPreferences, setReligiousPreferences] = useState<ReligionTag[]>([]);
  const [isEditing, setIsEditing] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [favoritePlaces, setFavoritePlaces] = useState<Place[]>([]);
  
  // Fetch user profile
  useEffect(() => {
    const fetchProfile = async () => {
      if (!user) return;
      
      try {
        // Get profile data
        const { data, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();
          
        if (error && error.code !== 'PGRST116') {
          console.error('Error fetching profile:', error);
          return;
        }
        
        // If profile doesn't exist, create it
        if (!data) {
          const newProfile: Omit<Profile, 'id'> = {
            full_name: null,
            avatar_url: null,
            dietary_preferences: [],
            religious_preferences: [],
            favorite_places: []
          };
          
          const { data: createdProfile, error: createError } = await supabase
            .from('profiles')
            .insert([{ id: user.id, ...newProfile }])
            .select()
            .single();
            
          if (createError) {
            console.error('Error creating profile:', createError);
            return;
          }
          
          setProfile(createdProfile as Profile);
          setName(createdProfile?.full_name || '');
          setDietaryPreferences(createdProfile?.dietary_preferences || []);
          setReligiousPreferences(createdProfile?.religious_preferences || []);
        } else {
          setProfile(data as Profile);
          setName(data.full_name || '');
          setDietaryPreferences(data.dietary_preferences || []);
          setReligiousPreferences(data.religious_preferences || []);
          
          // Fetch favorite places if any
          if (data.favorite_places && data.favorite_places.length > 0) {
            const { data: placesData, error: placesError } = await supabase
              .from('places')
              .select('*')
              .in('id', data.favorite_places);
              
            if (!placesError && placesData) {
              setFavoritePlaces(placesData as Place[]);
            }
          }
        }
      } catch (err) {
        console.error('Error in profile fetch:', err);
      } finally {
        setIsLoading(false);
      }
    };
    
    fetchProfile();
  }, [user]);
  
  // Toggle dietary preference
  const toggleDietaryPreference = (tag: DietaryTag) => {
    setDietaryPreferences(prev => 
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };
  
  // Toggle religious preference
  const toggleReligiousPreference = (tag: ReligionTag) => {
    setReligiousPreferences(prev => 
      prev.includes(tag)
        ? prev.filter(t => t !== tag)
        : [...prev, tag]
    );
  };
  
  // Save profile changes
  const saveProfile = async () => {
    if (!user) return;
    
    setIsLoading(true);
    
    try {
      const { error } = await supabase
        .from('profiles')
        .update({
          full_name: name,
          dietary_preferences: dietaryPreferences,
          religious_preferences: religiousPreferences,
        })
        .eq('id', user.id);
        
      if (error) {
        throw error;
      }
      
      setIsEditing(false);
      toastState.addToast('Profile updated successfully', 'success');
    } catch (err) {
      console.error('Error updating profile:', err);
      toastState.addToast('Failed to update profile', 'error');
    } finally {
      setIsLoading(false);
    }
  };
  
  // Handle sign out
  const handleSignOut = async () => {
    await signOut();
    navigate('/');
  };
  
  if (isLoading && !profile) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  return (
    <div className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
      <div className="bg-white shadow-card rounded-lg overflow-hidden">
        {/* Profile header */}
        <div className="bg-primary-600 h-32 relative">
          <div className="absolute -bottom-16 left-8">
            <div className="h-32 w-32 rounded-full bg-white p-1">
              <div className="h-full w-full rounded-full bg-gray-100 flex items-center justify-center">
                <User className="h-16 w-16 text-gray-400" />
              </div>
            </div>
          </div>
        </div>
        
        {/* Profile content */}
        <div className="pt-20 pb-6 px-6">
          <div className="flex justify-between items-center mb-8">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">
                {isEditing ? (
                  <input
                    type="text"
                    value={name}
                    onChange={(e) => setName(e.target.value)}
                    placeholder="Your name"
                    className="input"
                  />
                ) : (
                  profile?.full_name || user?.email || 'User'
                )}
              </h1>
              <p className="text-gray-500">{user?.email}</p>
            </div>
            
            <div className="flex space-x-3">
              {isEditing ? (
                <>
                  <button 
                    onClick={() => setIsEditing(false)} 
                    className="btn-outline"
                  >
                    Cancel
                  </button>
                  <button 
                    onClick={saveProfile}
                    className="btn-primary flex items-center"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-white mr-2"></div>
                        Saving...
                      </>
                    ) : (
                      'Save Changes'
                    )}
                  </button>
                </>
              ) : (
                <>
                  <button 
                    onClick={() => setIsEditing(true)}
                    className="btn-outline flex items-center"
                  >
                    <Settings className="h-4 w-4 mr-2" />
                    Edit Profile
                  </button>
                  <button 
                    onClick={handleSignOut}
                    className="btn-outline flex items-center"
                  >
                    <LogOut className="h-4 w-4 mr-2" />
                    Sign Out
                  </button>
                </>
              )}
            </div>
          </div>
          
          {/* Preferences section */}
          {isEditing && (
            <div className="mb-8">
              <h2 className="text-xl font-semibold text-gray-800 mb-4">Your Preferences</h2>
              
              <div className="mb-6">
                <h3 className="text-sm font-medium text-gray-700 mb-3">Dietary Preferences</h3>
                <div className="flex flex-wrap gap-2">
                  {dietaryOptions.map((option) => (
                    <button
                      key={option.value}
                      onClick={() => toggleDietaryPreference(option.value)}
                      className={`px-3 py-1 rounded-full text-sm font-medium transition-colors
                        ${dietaryPreferences.includes(option.value) 
                          ? 'bg-primary-100 text-primary-800' 
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
                    >
                      {option.label}
                    </button>
                  ))}
                </div>
              </div>
              
              <div>
                <h3 className="text-sm font-medium text-gray-700 mb-3">Religious Preferences</h3>
                <div className="flex flex-wrap gap-2">
                  {religionOptions.map((option) => (
                    <button
                      key={option.value}
                      onClick={() => toggleReligiousPreference(option.value)}
                      className={`px-3 py-1 rounded-full text-sm font-medium transition-colors
                        ${religiousPreferences.includes(option.value) 
                          ? 'bg-secondary-100 text-secondary-800' 
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'}`}
                    >
                      {option.label}
                    </button>
                  ))}
                </div>
              </div>
            </div>
          )}
          
          {/* Favorite places */}
          <div className="mt-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4">Your Saved Places</h2>
            
            {favoritePlaces.length > 0 ? (
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
                {favoritePlaces.map((place) => (
                  <PlaceCard key={place.id} place={place} />
                ))}
              </div>
            ) : (
              <div className="bg-gray-50 rounded-lg p-6 text-center">
                <p className="text-gray-600 mb-3">You haven't saved any places yet.</p>
                <Link to="/" className="text-primary-600 hover:text-primary-700 font-medium">
                  Start exploring places
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Profile;